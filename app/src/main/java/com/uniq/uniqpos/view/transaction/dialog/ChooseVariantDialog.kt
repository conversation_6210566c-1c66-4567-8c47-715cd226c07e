package com.uniq.uniqpos.view.transaction.dialog

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.ProductEntity
import com.uniq.uniqpos.databinding.DialogVariantBinding
import com.uniq.uniqpos.databinding.ListItemVariantBinding
import com.uniq.uniqpos.util.Constant
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.TransactionViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber


/**
 * Created by an<PERSON><PERSON><PERSON><PERSON> on 09/07/20.
 */

abstract class ChooseVariantDialog(context: Context,
                                   private val viewModel: TransactionViewModel,
                                   private val productId: Long) : AlertDialog(context) {

    private lateinit var binding: DialogVariantBinding
    private val productVariants = ArrayList<ProductEntity>()
    private val productList = ArrayList<ProductEntity>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_variant, null, false)
        setContentView(binding.root)
        window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        binding.recviewVariant.adapter = object : GlobalAdapter<ListItemVariantBinding>(R.layout.list_item_variant, productVariants) {
            override fun onBindViewHolder(holder: GlobalViewHolder<ListItemVariantBinding>, position: Int) {
                super.onBindViewHolder(holder, position)
                holder.itemView.setOnClickListener {
                    val variant = productVariants[position]
                    productList.firstOrNull { it.variantFkid == variant.variantFkid }?.let { product ->
                        checkStock(product) {
                            onItemSelected(product.copy(name = "${product.name} (${variant.name})"))
                        }
                    }
                    dismiss()
                }
            }
        }

        binding.txtCancel.setOnClickListener { dismiss() }

        loadData()
    }

    private fun loadData() {
        GlobalScope.launch(Dispatchers.IO) {
            val variantList = viewModel.getProductVariantById(productId)
            val products = viewModel.getProductListById(productId)
            productVariants.clear()
            productList.clear()
            productList.addAll(products)
            variantList.forEach { variant ->
                products.firstOrNull { it.variantFkid == variant.variantId }?.let { product ->
                    productVariants.add(product.copy(name = variant.variantName))
                }
            }
            Timber.i("variant list size : ${variantList.size} | product list size : ${products.size}")
            if(productVariants.size <= 10) Timber.i("variants : ${Gson().toJson(productVariants.map { it.name })}")
            launch(Dispatchers.Main) {
                binding.recviewVariant.adapter?.notifyDataSetChanged()
                if (productVariants.isEmpty()) {
                    Timber.i("variant is empty, product Id : $productId")
                    binding.txtCancel.text = context.getString(R.string.continue_without_variant)
                    binding.txtCancel.setOnClickListener {
                        productList.firstOrNull { it.productId == productId }?.let { product ->
                            onItemSelected(product)
                        }
                        dismiss()
                    }
                }
            }
        }
    }

    private fun checkStock(product: ProductEntity, listener: () -> Unit) {
        if (product.stock == Constant.STOCK_UNAVAILABLE || (product.stockQty == 0 && product.stockManagement == 1)) {
            context.showMessage(context.getString(R.string.question_adding_no_stock_product), context.getString(R.string.run_out), { _, _ ->
                listener()
            })
        } else {
            listener()
        }
    }



    abstract fun onItemSelected(productEntity: ProductEntity)

}