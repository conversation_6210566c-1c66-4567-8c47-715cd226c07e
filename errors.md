e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/data/remote/repository/PromotionRepository.kt:47:67 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/data/remote/repository/SalesRepository.kt:89:52 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/data/remote/repository/SalesRepository.kt:89:98 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:329:40 'fun StringBuilder.appendln(): StringBuilder' is deprecated. Use appendLine instead. Note that the new method always appends the line feed character '\n' regardless of the system line separator.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:331:39 'fun StringBuilder.appendln(): StringBuilder' is deprecated. Use appendLine instead. Note that the new method always appends the line feed character '\n' regardless of the system line separator.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:427:27 'fun StringBuilder.appendln(value: String?): StringBuilder' is deprecated. Use appendLine instead. Note that the new method always appends the line feed character '\n' regardless of the system line separator.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:432:30 'fun StringBuilder.appendln(value: String?): StringBuilder' is deprecated. Use appendLine instead. Note that the new method always appends the line feed character '\n' regardless of the system line separator.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:436:30 'fun StringBuilder.appendln(value: String?): StringBuilder' is deprecated. Use appendLine instead. Note that the new method always appends the line feed character '\n' regardless of the system line separator.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:923:24 'fun String.toLowerCase(locale: Locale): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:1133:36 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/CustomExtension.kt:1138:59 'fun Char.toUpperCase(): Char' is deprecated. Use uppercaseChar() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/printer/PrinterBluetooth.kt:472:24 'fun StringBuilder.appendln(value: String?): StringBuilder' is deprecated. Use appendLine instead. Note that the new method always appends the line feed character '\n' regardless of the system line separator.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/receipt/PrintNotaUtil.kt:746:107 'fun String.toUpperCase(): String' is deprecated. Use uppercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/util/view/DialogAuthorization.kt:30:1 Class 'DialogAuthorization' is not abstract and does not implement abstract member 'lifecycle'.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/cart/TransactionCartActivity.kt:250:116 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/cart/TransactionCartActivity.kt:250:141 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/cart/TransactionCartActivity.kt:250:168 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/cart/TransactionCartActivity.kt:250:193 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/help/HelpFragment.kt:46:48 'fun String.toUpperCase(): String' is deprecated. Use uppercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/help/HelpFragment.kt:53:48 'fun String.toUpperCase(): String' is deprecated. Use uppercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/help/HelpFragment.kt:93:30 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/help/HelpFragment.kt:93:55 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/ordersummary/OrderSummaryActivity.kt:140:27 Class '<anonymous>' is not abstract and does not implement abstract member 'lifecycle'.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/runoutstock/RunOutOfStockViewModel.kt:79:63 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
e: file:///workspace/android-pos/app/src/main/java/com/uniq/uniqpos/view/runoutstock/RunOutOfStockViewModel.kt:79:94 'fun String.toLowerCase(): String' is deprecated. Use lowercase() instead.
[Incubating] Problems report is available at: file:///workspace/android-pos/build/reports/problems/problems-report.html